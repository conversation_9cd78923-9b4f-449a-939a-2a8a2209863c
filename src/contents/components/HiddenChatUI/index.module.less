/* HiddenChatUI 样式文件 */

.hiddenChatUIContainer {
  /* 完全隐藏容器 */
  position: absolute !important;
  visibility: hidden !important;
  pointer-events: none !important;
  width: 0 !important;
  height: 0 !important;
  overflow: hidden !important;
  z-index: -1 !important;

  /* 确保不影响页面布局 */
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
  background: transparent !important;

  /* 防止任何可能的显示 */
  opacity: 0 !important;
  display: block !important;
  /* 保持display为block，但通过其他属性隐藏 */
}

/* 确保内部所有元素都被隐藏 */
.hiddenChatUIContainer * {
  visibility: hidden !important;
  pointer-events: none !important;
}

/* 防止ChatUI的样式影响页面 */
.hiddenChatUIContainer .chatui-root {
  position: static !important;
  width: 0 !important;
  height: 0 !important;
  overflow: hidden !important;
}

/* 确保任何可能的弹窗或浮层都被隐藏 */
.hiddenChatUIContainer .ant-modal,
.hiddenChatUIContainer .ant-drawer,
.hiddenChatUIContainer .ant-popover,
.hiddenChatUIContainer .ant-tooltip,
.hiddenChatUIContainer .ant-dropdown {
  display: none !important;
  visibility: hidden !important;
}

/* 隐藏所有可能的输入框和按钮 */
.hiddenChatUIContainer input,
.hiddenChatUIContainer button,
.hiddenChatUIContainer textarea,
.hiddenChatUIContainer select {
  display: none !important;
  visibility: hidden !important;
  pointer-events: none !important;
}